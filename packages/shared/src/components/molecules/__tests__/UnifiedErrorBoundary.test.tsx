/**
 * UnifiedErrorBoundary Tests
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  UnifiedErrorBoundary,
  AppErrorBoundary,
  FeatureErrorBoundary,
} from '../UnifiedErrorBoundary';

// Component that throws an error
const ErrorComponent = ({ shouldThrow = true }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('UnifiedErrorBoundary', () => {
  // Suppress console errors during tests
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should render children when no error occurs', () => {
    render(
      <UnifiedErrorBoundary>
        <div>Test content</div>
      </UnifiedErrorBoundary>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('should render fallback UI when an error occurs', () => {
    render(
      <UnifiedErrorBoundary>
        <ErrorComponent />
      </UnifiedErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(
      screen.getByText(
        "We're sorry, but an unexpected error has occurred. Please try reloading the application."
      )
    ).toBeInTheDocument();
    expect(screen.getByText('Reload Application')).toBeInTheDocument();
  });

  it('should render custom fallback when provided', () => {
    render(
      <UnifiedErrorBoundary
        fallback={({ error, resetError }) => (
          <div>
            <h2>Custom fallback</h2>
            <p>{error.message}</p>
            <button onClick={resetError}>Reset</button>
          </div>
        )}
      >
        <ErrorComponent />
      </UnifiedErrorBoundary>
    );

    expect(screen.getByText('Custom fallback')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
    expect(screen.getByText('Reset')).toBeInTheDocument();
  });

  it('should reset error when retry button is clicked', () => {
    const TestComponent = () => {
      const [key, setKey] = React.useState(0);
      const [shouldThrow, setShouldThrow] = React.useState(true);

      return (
        <UnifiedErrorBoundary key={key} resetOnPropsChange={true} isFeatureBoundary={true}>
          <ErrorComponent shouldThrow={shouldThrow} />
          <button
            onClick={() => {
              setShouldThrow(false);
              setKey((prev) => prev + 1); // Force remount to reset error boundary
            }}
            style={{ display: 'none' }}
          >
            Reset Component
          </button>
        </UnifiedErrorBoundary>
      );
    };

    render(<TestComponent />);

    // Initially shows error
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    // For component-level boundaries, we should see "Try Again" not "Reload Application"
    expect(screen.getByText('Try Again')).toBeInTheDocument();

    // Click retry button - this will call resetError() which resets the error boundary state
    fireEvent.click(screen.getByText('Try Again'));

    // The error boundary should reset and re-render the children
    // Since the ErrorComponent still has shouldThrow=true, it will throw again
    // This is expected behavior - the retry button resets the boundary but doesn't change the underlying cause
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });
});

describe('AppErrorBoundary', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should render app-level error UI when an error occurs', () => {
    render(
      <AppErrorBoundary>
        <ErrorComponent />
      </AppErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(
      screen.getByText(
        "We're sorry, but an unexpected error has occurred. Please try reloading the application."
      )
    ).toBeInTheDocument();
    expect(screen.getByText('Reload Application')).toBeInTheDocument();
  });
});

describe('FeatureErrorBoundary', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should render feature-level error UI when an error occurs', () => {
    render(
      <FeatureErrorBoundary featureName="Test Feature">
        <ErrorComponent />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('Error in Test Feature')).toBeInTheDocument();
    expect(
      screen.getByText('We encountered a problem while loading Test Feature. You can try again.')
    ).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('should show skip button when onSkip is provided', () => {
    const onSkip = vi.fn();

    render(
      <FeatureErrorBoundary featureName="Test Feature" onSkip={onSkip}>
        <ErrorComponent />
      </FeatureErrorBoundary>
    );

    expect(screen.getByText('Skip This Feature')).toBeInTheDocument();

    // Click skip button
    fireEvent.click(screen.getByText('Skip This Feature'));

    // onSkip should be called
    expect(onSkip).toHaveBeenCalledTimes(1);
  });
});
